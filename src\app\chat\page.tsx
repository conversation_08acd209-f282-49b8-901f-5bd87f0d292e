'use client';
import { useState, useRef } from 'react';

export default function ChatPage() {
  const [prompt, setPrompt] = useState('');
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      generateImage();
    }
  };

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  };

  const generateImage = async () => {
    if (!prompt.trim()) return;
    setLoading(true);
    try {
      const res = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt }),
      });
      const data = await res.json();
      setImageUrl(data.url);
    } catch (err) {
      console.error(err);
      alert('Error generating image.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center min-h-screen p-4 bg-gray-50">
      <h1 className="text-2xl font-semibold mb-6 text-gray-800">AI Image Generator</h1>

      <div className="w-full max-w-2xl mb-6">
        <div className="relative bg-white rounded-lg border border-gray-300 shadow-sm">
          <textarea
            ref={textareaRef}
            value={prompt}
            onChange={(e) => {
              setPrompt(e.target.value);
              adjustTextareaHeight();
            }}
            onKeyDown={handleKeyDown}
            placeholder="Describe the image you want to generate... (Press Enter to generate, Shift+Enter for new line)"
            disabled={loading}
            rows={3}
            className="w-full p-4 pr-16 resize-none border-0 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ minHeight: '80px', maxHeight: '200px' }}
          />
          <button
            onClick={generateImage}
            disabled={loading || !prompt.trim()}
            className="absolute bottom-3 right-3 p-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center"
            style={{ width: '40px', height: '40px' }}
          >
            {loading ? (
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
            ) : (
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M5 12l5 5L20 7" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {loading && (
        <div className="flex items-center justify-center mb-6">
          <div className="animate-spin rounded-full h-8 w-8 border-4 border-green-600 border-t-transparent mr-3"></div>
          <span className="text-gray-600">Generating your image...</span>
        </div>
      )}

      {imageUrl && (
        <div className="w-full max-w-2xl">
          <img
            src={imageUrl}
            alt="Generated"
            className="w-full rounded-lg shadow-lg"
          />
        </div>
      )}
    </div>
  );
}