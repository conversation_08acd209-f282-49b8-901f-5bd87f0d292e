'use client';
import { useState } from 'react';

export default function ChatPage() {
  const [prompt, setPrompt] = useState('');
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const generateImage = async () => {
    if (!prompt) return;
    setLoading(true);
    try {
      const res = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt }),
      });
      const data = await res.json();
      setImageUrl(data.url);
    } catch (err) {
      console.error(err);
      alert('Error generating image.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center min-h-screen p-4">
      <h1 className="text-2xl font-semibold mb-4">AI Image Generator</h1>
      <div className="flex w-full max-w-xl mb-6">
        <input
          type="text"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="Enter image prompt"
          disabled={loading}
          className="flex-grow p-2 border rounded-l"
        />
        <button
          onClick={generateImage}
          disabled={loading}
          className="px-4 bg-green-600 text-white rounded-r hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? 'Generating...' : 'Generate'}
        </button>
      </div>
      {imageUrl && (
        <img
          src={imageUrl}
          alt="Generated"
          className="max-w-full rounded shadow"
        />
      )}
    </div>
  );
}